from langchain_ollama.llms import OllamaLLM 
from langchain_core.prompts import ChatPromptTemplate
from vector import retriever
from langchain_community.vectorstores import Chroma


model = OllamaLLM(model="llama3.2:3b")

template = """
You are expert in the answering questions about the pizza restaurant.

here are some relevant reviews: {reviews}

here is the question to answer: {question}  
"""

prompt = ChatPromptTemplate.from_template(template)

chain = prompt | model 

while True:
    print("\n\n----------------------------")
    question = input("Enter your question(q to quit): ")
    print("\n")
    if question == "q":
        break

    reviews = retriever.invoke(question)

    result  = chain.invoke({"reviews": reviews, "question": question})
    print(result)
    print(reviews)
